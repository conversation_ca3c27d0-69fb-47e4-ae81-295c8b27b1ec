@extends('admin.layouts.app')

@section('title', 'View Branch')

@section('header', 'Branch Details')

@section('content')
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
                <h2 class="text-xl font-bold text-gray-800 mb-2">
                    <i class="fas fa-building mr-2 text-primary-color"></i>
                    {{ $branch->name }}
                </h2>
                <p class="text-gray-600">View branch details and manage settings</p>
            </div>
            
            <div class="flex flex-wrap gap-2">
                <form action="{{ route('admin.branches.toggle-active', $branch) }}" method="POST" class="inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="bg-{{ $branch->active ? 'red' : 'green' }}-600 hover:bg-{{ $branch->active ? 'red' : 'green' }}-700 text-white px-4 py-2 rounded-md font-medium">
                        <i class="fas fa-{{ $branch->active ? 'eye-slash' : 'eye' }} mr-2"></i>
                        {{ $branch->active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                
                <a href="{{ route('admin.branches.edit', $branch) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                
                <a href="{{ route('admin.branches.index') }}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md font-medium">
                    <i class="fas fa-arrow-left mr-2"></i> Back to List
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Branch Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                    Branch Information
                </h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Branch Name</label>
                        <p class="text-gray-900 font-semibold">{{ $branch->name }}</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                            <p class="text-gray-900">{{ $branch->city }}</p>
                        </div>
                        
                        @if($branch->province)
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Province</label>
                                <p class="text-gray-900">{{ $branch->province }}</p>
                            </div>
                        @endif
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <p class="text-gray-900">{{ $branch->address }}</p>
                        <p class="text-sm text-gray-600 mt-1">{{ $branch->full_address }}</p>
                    </div>
                    
                    @if($branch->manager_name)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Manager</label>
                            <p class="text-gray-900">{{ $branch->manager_name }}</p>
                        </div>
                    @endif
                    
                    @if($branch->operating_hours)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Operating Hours</label>
                            <p class="text-gray-900">{{ $branch->formatted_operating_hours }}</p>
                        </div>
                    @endif
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <span class="inline-flex items-center px-3 py-1 bg-{{ $branch->active ? 'green' : 'red' }}-100 text-{{ $branch->active ? 'green' : 'red' }}-800 rounded-full text-sm font-medium">
                                <i class="fas fa-circle mr-1 text-xs"></i>
                                {{ $branch->active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                            <p class="text-gray-900">{{ $branch->sort_order ?? 'Not set' }}</p>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <p class="text-gray-900">{{ $branch->created_at->format('M d, Y \a\t H:i') }}</p>
                    </div>
                    
                    @if($branch->updated_at != $branch->created_at)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                            <p class="text-gray-900">{{ $branch->updated_at->format('M d, Y \a\t H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-address-book mr-2 text-green-500"></i>
                    Contact Information
                </h3>
                
                <div class="space-y-4">
                    @if($branch->phone)
                        <div class="flex items-center p-3 bg-white rounded-lg border">
                            <div class="flex-shrink-0">
                                <i class="fas fa-phone text-primary-color text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <label class="block text-sm font-medium text-gray-700">Phone</label>
                                <a href="tel:{{ $branch->phone }}" class="text-gray-900 hover:text-primary-color">
                                    {{ $branch->phone }}
                                </a>
                            </div>
                        </div>
                    @endif
                    
                    @if($branch->email)
                        <div class="flex items-center p-3 bg-white rounded-lg border">
                            <div class="flex-shrink-0">
                                <i class="fas fa-envelope text-primary-color text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <label class="block text-sm font-medium text-gray-700">Email</label>
                                <a href="mailto:{{ $branch->email }}" class="text-gray-900 hover:text-primary-color">
                                    {{ $branch->email }}
                                </a>
                            </div>
                        </div>
                    @endif
                    
                    <div class="flex items-start p-3 bg-white rounded-lg border">
                        <div class="flex-shrink-0">
                            <i class="fas fa-map-marker-alt text-primary-color text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="text-gray-900">{{ $branch->full_address }}</p>
                            @if($branch->latitude && $branch->longitude)
                                <p class="text-sm text-gray-600 mt-1">
                                    <i class="fas fa-map-pin mr-1"></i>
                                    {{ $branch->latitude }}, {{ $branch->longitude }}
                                </p>
                            @endif
                        </div>
                    </div>
                    
                    @if(!$branch->phone && !$branch->email)
                        <div class="text-center py-8">
                            <i class="fas fa-phone-slash text-gray-400 text-3xl mb-2"></i>
                            <p class="text-gray-500">No contact information available</p>
                            <a href="{{ route('admin.branches.edit', $branch) }}" class="text-primary-color hover:text-secondary-color text-sm">
                                Add contact details
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        @if($branch->latitude && $branch->longitude)
            <!-- Map Section -->
            <div class="mt-6 bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    <i class="fas fa-map mr-2 text-red-500"></i>
                    Location Map
                </h3>

                <div class="bg-white rounded-lg border p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-sm text-gray-600">Coordinates</p>
                            <p class="font-mono text-sm">{{ $branch->latitude }}, {{ $branch->longitude }}</p>
                        </div>
                        <div class="flex gap-2">
                            <a href="https://www.google.com/maps?q={{ $branch->latitude }},{{ $branch->longitude }}"
                               target="_blank"
                               class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                Google Maps
                            </a>
                            <a href="https://maps.apple.com/?q={{ $branch->latitude }},{{ $branch->longitude }}"
                               target="_blank"
                               class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                Apple Maps
                            </a>
                        </div>
                    </div>

                    <!-- Embedded Map Placeholder -->
                    <div class="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-map-marked-alt text-gray-400 text-4xl mb-2"></i>
                            <p class="text-gray-600">Interactive map would be displayed here</p>
                            <p class="text-sm text-gray-500">Click the links above to view in mapping applications</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Status Information -->
        <div class="mt-6 p-4 rounded-lg {{ $branch->active ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200' }}">
            <div class="flex items-center">
                <i class="fas fa-{{ $branch->active ? 'check-circle' : 'exclamation-triangle' }} text-{{ $branch->active ? 'green' : 'red' }}-600 mr-2"></i>
                <p class="text-{{ $branch->active ? 'green' : 'red' }}-800 font-medium">
                    This branch is {{ $branch->active ? 'active and visible to the public' : 'inactive and hidden from the public' }}
                </p>
            </div>
            @if(!$branch->active)
                <p class="text-red-700 text-sm mt-1">
                    Activate this branch to make it visible on the website.
                </p>
            @endif
        </div>
    </div>
@endsection
